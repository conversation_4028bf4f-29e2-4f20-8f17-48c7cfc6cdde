# Permission Flow Test Plan

## Current Issue
User reports that on boot, the app only asks for notification permission, not location permission.

## Expected Flow
1. App starts at splash screen
2. After splash loads, if user is authenticated but doesn't have location permission, redirect to permission view
3. Permission view should show location permission request first
4. After location permission is handled, show notification permission

## Debug Steps Added
1. Added debug logging to router redirect logic to see permission states
2. Added debug logging to permission providers to see what permissions are detected
3. Added debug logging to position provider to see permission status checks

## Test Scenarios

### Scenario 1: Fresh Install (No Permissions Granted)
- Expected: Should redirect to permission view and show location permission request
- Debug: Check logs for permission states

### Scenario 2: Location Already Granted
- Expected: Should go directly to explore view (no permission view)
- Debug: Check if location permission is somehow already granted

### Scenario 3: Location Denied, Notification Not Asked
- Expected: Should redirect to permission view and show location permission request
- Debug: Check permission status values

## Files Modified
1. `lib/app/core/router/router.dart` - Added debug logging to redirect logic
2. `lib/app/features/main/provider/permission_providers.dart` - Added debug logging to permission state
3. `lib/app/features/maps/provider/position_providers.dart` - Added debug logging to position checks

## Next Steps
1. Run the app and check debug logs to understand current permission states
2. If location permission is already granted, test with a fresh install or reset permissions
3. If router redirect isn't working, check the redirect logic flow
4. If PermissionView is showing wrong content, check the view logic

## Potential Issues
1. Location permission might be automatically granted on some devices/simulators
2. The permission check might be returning true when it shouldn't
3. The router redirect logic might have a timing issue
4. The PermissionView might have conditional logic that prioritizes notification permission
