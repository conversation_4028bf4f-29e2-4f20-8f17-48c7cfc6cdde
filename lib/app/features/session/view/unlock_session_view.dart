import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:geolocator/geolocator.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/view/auth_view.dart';
import 'package:gomama/app/features/explore/provider/show_session_island_providers.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:gomama/app/features/session/model/sessions.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:gomama/app/features/session/view/current_session_view.dart';
import 'package:gomama/app/features/session/view/session_clean_notice_view.dart';
import 'package:gomama/app/features/session/view/session_expired_notice_view.dart';
import 'package:gomama/app/features/session/view/unlock_session_demo_video_view.dart';
import 'package:gomama/app/features/verification/provider/verification_providers.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:gomama/app/widgets/loading_view.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:rive/rive.dart';

class UnlockListingViewSheet extends ConsumerStatefulWidget {
  const UnlockListingViewSheet({
    super.key,
    this.listingId,
    this.duration,
  });
  final String? listingId;
  final int? duration;

  @override
  ConsumerState<UnlockListingViewSheet> createState() =>
      _UnlockListingViewSheetState();
}

class _UnlockListingViewSheetState
    extends ConsumerState<UnlockListingViewSheet> {
  int _pinState = 0; // 0: one time, 1: daily, 2: custom

  void _cyclePinState() {
    setState(() {
      if (_pinState == 0) {
        _pinState = 1; // one time  -> daily
      } else if (_pinState == 1) {
        // daily -> custom (only if available)
        final session = ref.read(activeSessionProvider).value?.session;
        if (session?.lockCustomPin != null) {
          _pinState = 2;
        } else {
          _pinState = 1; // stay on daily if custom not available
        }
      } else {
        _pinState = 1; // custom -> daily
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final devicePosition = ref.watch(currentPositionProvider).valueOrNull;

    return Stack(
      children: [
        const RiveAnimation.asset(
          'assets/rives/generate_pro.riv',
          fit: BoxFit.cover,
          alignment: Alignment.center,
          animations: ['Timeline 1'],
        ),
        SafeArea(
          child: Column(
            children: [
              const UnlockSessionDemoVideoView(),
              const SizedBox(height: 16),
              _Body(
                widget.listingId,
                widget.duration,
                devicePosition,
                _pinState,
              ),
              const Spacer(),
              _Footer(onRegeneratePin: _cyclePinState),
            ],
          ),
        ),
        Positioned(
          top: kTextTabBarHeight,
          right: 16,
          child: CloseButton(
            onPressed: () {
              context
                ..pop()
                ..pop();
            },
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(Colors.white),
              iconColor: WidgetStateProperty.all(CustomColors.primary),
            ),
          ),
        ),
      ],
    );
  }
}

class _Body extends StatefulHookConsumerWidget {
  const _Body(this.id, this.duration, this.devicePositon, this.pinState);
  final String? id;
  final int? duration;
  final Position? devicePositon;
  final int pinState;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => __BodyState();
}

class __BodyState extends ConsumerState<_Body> {
  CreateSessionInput? input;

  void createSession() {
    // make sure sse is connected before we unlock
    ref.read(activeSessionProvider).when(
      error: (error, stackTrace) {
        Groveman.error(
          'ListingView - activeSession',
          error: error,
          stackTrace: stackTrace,
        );

        /// NOTE: default might have error from previous session
        ///       handle activeSessionError that we support
        if (handledActiveSessionError(
          error as Exception,
          withDefault: false,
        )) {
          if (input == null) {
            Groveman.warning('create session input is null');
            return;
          }

          // let user try again
          ref.read(createSessionProvider(input!).future).then((session) {
            Groveman.info('create session success');
          }).onError((error, stackTrace) {
            Groveman.warning('create session error');
          });
        } else {
          // revalidate active session
          // TODO(kkcy): revisit this!!!
          ref.invalidate(activeSessionProvider);
          // try to create again
          Future.delayed(const Duration(milliseconds: 500), createSession);
        }
      },
      loading: () {
        // wait & do nothing
      },
      data: (session) {
        // if entered data state, connection is established
        // generate new session
        ref.read(createSessionProvider(input!).future).then((session) {
          Groveman.info('create session success');
          ref
              .read(showSessionIslandProvider.notifier)
              .show(); // show session island when successfully created a session
        }).onError((error, stackTrace) {
          Groveman.warning('create session error');
        });
      },
    );
  }

  @override
  void initState() {
    super.initState();

    if (widget.id != null && widget.devicePositon != null) {
      input = CreateSessionInput(
        listingId: widget.id!,
        duration: widget.duration!,
        lat: widget.devicePositon!.latitude,
        lon: widget.devicePositon!.longitude,
      );

      WidgetsBinding.instance.addPostFrameCallback((_) {
        createSession();
      });
    } else {
      input = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if location permission is available
    if (widget.devicePositon == null) {
      return Column(
        children: [
          const Icon(
            Icons.location_off,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'Location Access Required',
            style: textTheme(context).labelLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'To unlock a pod and create a session, location access is required to verify you are near the pod.',
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: FilledButton(
              onPressed: () {
                Navigator.of(context).pop();
                const PermissionRoute().push(context);
              },
              child: const Text('Enable Location Access'),
            ),
          ),
        ],
      );
    }

    final sessionEventAsync = ref.watch(activeSessionProvider);
    // Keep track of the last valid session to prevent flashing
    // final lastValidSessionEvent = useState<SessionEvent?>(null);

    // Update lastValidSessionEvent when we get new data
    // useEffect(
    //   () {
    //     if (sessionEventAsync.value != null &&
    //         sessionEventAsync.value?.session != null) {
    //       lastValidSessionEvent.value = sessionEventAsync.value;
    //     }

    //     return null;
    //   },
    //   [sessionEventAsync.value],
    // );

    // Show error state
    if (sessionEventAsync.hasError) {
      Groveman.error(
        'UnlockSessionView',
        error: sessionEventAsync.error,
        stackTrace: sessionEventAsync.stackTrace,
      );

      // If we have a last valid session, show that instead of empty state
      // if (lastValidSessionEvent.value?.session != null) {
      //   return _ContentView(
      //     lastValidSessionEvent.value!.session!,
      //     input: input,
      //   );
      // }

      return _SessionError(sessionEventAsync.error!, input);
    }

    // Show loading state only if we don't have a last valid session
    // if (sessionEventAsync.isLoading && lastValidSessionEvent.value == null) {
    if (sessionEventAsync.isLoading) {
      return Column(
        children: [
          Text(
            'Processing...',
            style: textTheme(context).labelLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          const SizedBox(height: 200, child: LoadingView()),
        ],
      );
    }

    // Use the current session if available, otherwise fall back to last valid session
    // final sessionEvent = sessionEventAsync.value ?? lastValidSessionEvent.value;
    final sessionEvent = sessionEventAsync.value;

    // something went wrong
    if (sessionEvent == null) {
      return const SizedBox.shrink();
    }

    /// NOTE: technically should never occur
    // if (sessionEvent.type == 'session_cleanup' &&
    //     lastValidSessionEvent.value != null) {
    //   return SessionCleanNoticeView(
    //     lastValidSessionEvent.value!.session!.id,
    //     true,
    //     input != null,
    //   );
    // }
    if (sessionEvent.type == 'session_cleanup' && sessionEventAsync.hasValue) {
      return SessionCleanNoticeView(
        sessionEventAsync.value!.session!.id,
        true,
        input != null,
      );
    }

    if (sessionEvent.type == 'entry_expired') {
      return const SessionExpiredNoticeView(
        true,
        true,
      );
    }

    if (sessionEvent.type == 'no_active_session' &&
        sessionEvent.session == null) {
      return Column(
        children: [
          Text(
            'Generating the PIN...',
            style: textTheme(context).labelLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          const SizedBox(height: 200, child: LoadingView()),
        ],
      );
    }

    if (sessionEvent.session == null) {
      return const SizedBox.shrink();
    }

    return _ContentView(
      sessionEvent.session!,
      input: input,
      pinState: widget.pinState,
    );
  }
}

class _ContentView extends ConsumerStatefulWidget {
  const _ContentView(
    this.session, {
    this.input,
    required this.pinState,
  });
  final Session session;
  final CreateSessionInput? input;
  final int pinState;

  @override
  ConsumerState<_ContentView> createState() => _ContentViewState();
}

class _ContentViewState extends ConsumerState<_ContentView> {
  String getPinToShow() {
    switch (widget.pinState) {
      case 0:
        return widget.session.lockOneTimePin ?? '';
      case 1:
        return widget.session.lockDailyPin ?? '';
      case 2:
        return widget.session.lockDailyPin ?? '';
      default:
        return widget.session.lockOneTimePin ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          'Enter PIN on the door lock',
          style: textTheme(context).labelLarge,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        _PinCard(session: widget.session, pinToShow: getPinToShow()),
        // only if first time unlocking
        if (widget.input != null) ...[
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: BrandButton.cta(
              child: const Text("Let's Go"),
              onPressed: () {
                context.pop();
                showCupertinoModalPopup(
                  context: context,
                  builder: (context) {
                    return const CurrentSessionView();
                  },
                );
              },
            ),
          ),
        ],
      ],
    );
  }
}

class _SessionError extends ConsumerWidget {
  const _SessionError(this.error, this.input);
  final Object error;
  final CreateSessionInput? input;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Card(
            color: Colors.white,
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 16,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (handledActiveSessionError(error as Exception))
                    Text(
                      error.toString().replaceAll('Exception: ', ''),
                      style: textTheme(context).labelLarge,
                      textAlign: TextAlign.center,
                    )
                  else ...[
                    Text(
                      'Something went wrong',
                      style: textTheme(context).labelLarge,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Center(
                      child: SizedBox(
                        width: 160,
                        child: BrandButton.singpass(
                          padding: EdgeInsets.zero,
                          onPressed: () {
                            if (input != null) {
                              ref.invalidate(createSessionProvider(input!));
                            }
                          },
                          child: const Text('RETRY'),
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),
                    Text(
                      'If the issue persists,\nplease contact our customer support',
                      style: textTheme(context).labelMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Center(
                      child: SizedBox(
                        width: 160,
                        child: BrandButton.whatsapp(
                          padding: EdgeInsets.zero,
                          onPressed: () {},
                          child: const Text('WhatsApp Us'),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
        // if listing.status == 'idle' && last_session.user_id == user.id
        // we show a button to kill session & allow them to restart
        if (canRestartActiveSession(error as Exception)) ...[
          const SizedBox(height: 16),
          Center(
            child: SizedBox(
              width: 160,
              child: BrandButton.singpass(
                padding: EdgeInsets.zero,
                onPressed: () async {
                  // TODO(kkcy): do we need to make sure socket is alive
                  await ref.read(endSessionProvider(isRestart: true).future);

                  if (context.mounted) {
                    context.pop();
                  }
                },
                child: const Text('RETRY'),
              ),
            ),
          ),
        ],
      ],
    );
  }
}

class _PinCard extends StatelessWidget {
  const _PinCard({
    required this.session,
    required this.pinToShow,
  });
  final Session session;
  final String pinToShow;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Card(
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
          child: SizedBox(
            width: double.infinity,
            child: Column(
              children: [
                Text(
                  pinToShow,
                  style: Theme.of(context)
                      .textTheme
                      .displayLarge!
                      .copyWith(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                Text(
                  '*One-time use only',
                  style: textTheme(context).labelMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _Footer extends HookConsumerWidget {
  const _Footer({this.onRegeneratePin});
  final VoidCallback? onRegeneratePin;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activeSession = ref.watch(activeSessionProvider);
    final _debounceTimer = useState<Timer?>(null);
    final _regenerating = useState(false);

    return activeSession.when(
      error: (error, stackTrace) {
        return const SizedBox.shrink();
      },
      loading: () {
        return const SizedBox.shrink();
      },
      data: (session) {
        if (session == null ||
            session.type == 'session_cleanup' ||
            session.type == 'entry_expired') {
          return const SizedBox.shrink();
        }

        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'For PIN issues,\nclick here to regenerate a new one',
                style: textTheme(context).labelMedium,
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 12),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: BrandButton.outlined(
                onPressed: _regenerating.value
                    ? null
                    : () {
                        if (_debounceTimer.value?.isActive ?? false) return;

                        _debounceTimer.value = Timer(
                          const Duration(milliseconds: 400),
                          () {
                            _regenerating.value = true;
                            onRegeneratePin?.call();
                            _regenerating.value = false;
                          },
                        );
                      },
                child: Text(
                  _regenerating.value ? 'Generating...' : 'Generate New Pin',
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }
}
