import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/main/provider/permission_providers.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionView extends HookConsumerWidget {
  const PermissionView({super.key});

  static const routeName = 'permission';
  static const routePath = '/permission';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Only refresh permission state when returning from Settings
    // Don't automatically re-request permissions to avoid spam
    useOnAppLifecycleStateChange(
      (previous, current) {
        if (current == AppLifecycleState.resumed) {
          // Only refresh the permission state, don't re-request
          ref.invalidate(permissionsProvider);
        }
      },
    );

    // Check current permission state
    final permissionState = ref.watch(permissionsProvider).valueOrNull;
    final hasLocationPermission =
        permissionState?.hasLocationPermission ?? false;
    final hasNotificationPermission =
        permissionState?.hasNotificationPermission ?? false;
    final needsNotificationPermission =
        permissionState != null && !hasNotificationPermission;

    // Track if we've already asked for permissions to avoid spam
    final hasAskedForLocationPermission = useState(false);
    final hasAskedForNotificationPermission = useState(false);

    // If both permissions are granted, show success state
    if (hasLocationPermission && hasNotificationPermission) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Permissions'),
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle,
                size: 80,
                color: Colors.green,
              ),
              SizedBox(height: 24),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 24),
                child: Text(
                  'All Permissions Enabled',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
              ),
              SizedBox(height: 16),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 24),
                child: Text(
                  "You're all set! GoMama has access to location and notifications to provide you with the best experience.",
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Permissions'),
      ),
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 48),
            // Show different icon based on location permission status
            Icon(
              hasLocationPermission
                  ? Icons.location_on
                  : Icons.location_on_outlined,
              size: 64,
              color: hasLocationPermission ? Colors.green : Colors.blue,
            ),
            const SizedBox(height: 24),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                hasLocationPermission
                    ? 'Location Access Enabled'
                    : 'Location Access Required',
                textAlign: TextAlign.center,
                style:
                    const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                hasLocationPermission
                    ? 'Great! You can now find nearby pods and access location-specific content.'
                    : 'To use location-based features like finding nearby pods and accessing location-specific content, GoMama needs access to your location.',
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: 16),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                'Features that require location access:',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
              ),
            ),
            const SizedBox(height: 8),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 40),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('• Find and access nearby Go!Mama pods'),
                  Text('• Get distance-based recommendations'),
                  Text('• Access location-specific content'),
                ],
              ),
            ),
            const SizedBox(height: 24),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: hasLocationPermission
                  ? FilledButton(
                      onPressed: () {
                        context.go('/explore');
                      },
                      style: FilledButton.styleFrom(
                        minimumSize: const Size(double.infinity, 48),
                        backgroundColor: Colors.green,
                      ),
                      child: const Text('Continue to App'),
                    )
                  : FilledButton(
                      onPressed: () async {
                        if (!hasAskedForLocationPermission.value) {
                          // First time - try to request permission directly
                          hasAskedForLocationPermission.value = true;

                          // Request permission directly first
                          final permission =
                              await Permission.locationWhenInUse.request();

                          if (permission.isGranted) {
                            // Permission granted, refresh providers and navigate to main app
                            ref
                              ..invalidate(permissionsProvider)
                              ..invalidate(currentPositionProvider);
                            if (context.mounted) {
                              context.go('/explore');
                            }
                          }
                          // If not granted, user can still use the Settings button
                        } else {
                          // User already denied once, take them to Settings
                          await openAppSettings();
                        }
                      },
                      style: FilledButton.styleFrom(
                        minimumSize: const Size(double.infinity, 48),
                      ),
                      child: Text(
                        hasAskedForLocationPermission.value
                            ? 'Open Settings to Enable Location'
                            : 'Allow Location Access',
                      ),
                    ),
            ),
            if (!hasLocationPermission) ...[
              const SizedBox(height: 12),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: OutlinedButton(
                  onPressed: () {
                    // Navigate to the main app without permissions
                    context.go('/explore');
                  },
                  style: OutlinedButton.styleFrom(
                    minimumSize: const Size(double.infinity, 48),
                  ),
                  child: const Text('Continue without location features'),
                ),
              ),
            ],
            const SizedBox(height: 16),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                'You can enable location access later in Settings > Privacy & Security > Location Services',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ),
            if (needsNotificationPermission || hasNotificationPermission) ...[
              const SizedBox(height: 32),
              Icon(
                hasNotificationPermission
                    ? Icons.notifications
                    : Icons.notifications_outlined,
                size: 48,
                color: hasNotificationPermission ? Colors.green : Colors.orange,
              ),
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Text(
                  hasNotificationPermission
                      ? 'Notifications Enabled'
                      : 'Stay Updated with Notifications',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Text(
                  hasNotificationPermission
                      ? "You'll receive important updates and pod session status."
                      : 'Enable notifications to receive important updates and pod session status.',
                  style: const TextStyle(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 16),
              if (!hasNotificationPermission) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: OutlinedButton(
                    onPressed: () async {
                      if (!hasAskedForNotificationPermission.value) {
                        // First time - try to request permission directly
                        hasAskedForNotificationPermission.value = true;

                        // Request notification permission directly first
                        final permission =
                            await Permission.notification.request();

                        if (permission.isGranted) {
                          // Permission granted, refresh providers and navigate to main app
                          ref.invalidate(permissionsProvider);
                          if (context.mounted) {
                            context.go('/explore');
                          }
                        }
                        // If not granted, user can still use the Settings button
                      } else {
                        // User already denied once, take them to Settings
                        await openAppSettings();
                      }
                    },
                    style: OutlinedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 48),
                    ),
                    child: Text(
                      hasAskedForNotificationPermission.value
                          ? 'Enable Notifications in Settings'
                          : 'Allow Notifications',
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: () {
                    // Navigate to the main app without notifications
                    context.go('/explore');
                  },
                  child: const Text('Skip for now'),
                ),
              ],
            ],
            const SizedBox(height: 48),
          ],
        ),
      ),
    );
  }
}
