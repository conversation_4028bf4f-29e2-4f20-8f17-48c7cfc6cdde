import 'package:geolocator/geolocator.dart';
import 'package:groveman/groveman.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'position_providers.g.dart';

@riverpod
FutureOr<Position?> currentPosition(CurrentPositionRef ref) async {
  // Check permission status without requesting to avoid spam
  final permission = await Permission.locationWhenInUse.status;

  // Debug logging to understand permission status
  Groveman.debug('CurrentPosition - Permission status: $permission');

  if (permission.isGranted) {
    try {
      final position = await Geolocator.getCurrentPosition();
      Groveman.debug('CurrentPosition - Got position successfully');
      return position;
    } catch (e) {
      // If location services are disabled or other error, return null
      Groveman.debug('CurrentPosition - Error getting position: $e');
      return null;
    }
  }

  Groveman.debug('CurrentPosition - Permission not granted, returning null');
  return null;
}
